#ifndef QRCODE_H
#define QRCODE_H

#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
// 包含官方QR码库的头文件
#include "../managed_components/espressif__qrcode/qrcodegen.h"

// 二维码模块大小 (像素)
#define QR_MODULE_SIZE 4

// 二维码边距 (模块数)
#define QR_MARGIN 4

#ifdef __cplusplus
extern "C" {
#endif

// 在LCD上显示二维码
// qr_data: 要编码的文本
// x, y: 二维码在屏幕上的位置
void display_qrcode_on_lcd(const char* qr_data, int x, int y);

// 将二维码以ASCII字符形式输出到终端日志
// qr_data: 要编码的文本
void print_qrcode_to_terminal(const char* qr_data);

#ifdef __cplusplus
}
#endif

#endif // QRCODE_H
