# ESP32 二维码蓝牙配网单元测试文档

## 1. 项目概述

本文档详细描述了从 xiaozhi-esp32_6hao 项目中提取的二维码蓝牙配网功能的单元测试规范。该功能基于 ESP-IDF 标准 BLE Provisioning 协议，支持通过微信小程序进行WiFi配网。

## 2. 核心模块分析

### 2.1 主要文件结构
```
ble_provisioning/
├── src/
│   ├── ble_provisioning.c      # 蓝牙配网核心实现
│   ├── qrcode.c               # 二维码生成和显示
│   └── ssid_manager.c         # WiFi凭证管理
├── include/
│   ├── ble_provisioning.h     # 蓝牙配网接口定义
│   ├── qrcode.h              # 二维码接口定义
│   └── ssid_manager.h        # WiFi凭证管理接口
└── test/
    ├── test_ble_provisioning.c
    ├── test_qrcode.c
    └── test_ssid_manager.c
```

### 2.2 依赖组件
- ESP-IDF WiFi Provisioning Manager
- ESP-IDF BLE Stack
- QRCode Generator (espressif/qrcode)
- NVS Flash Storage
- FreeRTOS Event Groups

## 3. 核心API接口

### 3.1 BLE配网接口 (ble_provisioning.h)
```c
// 启动蓝牙配网服务
bool ble_prov_start(const char *device_name, const char *pop, bool wait_for_completion);

// 检查配网状态
bool ble_prov_is_provisioned(void);

// 停止配网服务
void ble_prov_stop(void);

// 生成标准ESP BLE Provisioning二维码
void print_qr_code_info(const char *device_name, const char *pop);
```

### 3.2 二维码接口 (qrcode.h)
```c
// 在终端打印二维码
void print_qrcode_to_terminal(const char* qr_data);

// 在LCD显示二维码（可选实现）
void display_qrcode_on_lcd(const char* qr_data, int x, int y);
```

### 3.3 WiFi凭证管理接口 (ssid_manager.h)
```c
// 单例模式获取实例
SsidManager& GetInstance();

// 添加WiFi凭证
void AddSsid(const std::string& ssid, const std::string& password);

// 获取WiFi列表
const std::vector<SsidItem>& GetSsidList() const;

// 清除所有WiFi凭证
void Clear();

// 移除指定WiFi
void RemoveSsid(int index);

// 设置默认WiFi
void SetDefaultSsid(int index);
```

## 4. 单元测试用例设计

### 4.1 BLE配网模块测试 (test_ble_provisioning.c)

#### 4.1.1 基础功能测试
```c
// 测试用例1: 配网服务启动测试
TEST_CASE("BLE provisioning start test", "[ble_prov]")
{
    // 测试正常启动
    bool result = ble_prov_start("PROV_TEST01", "123456", false);
    TEST_ASSERT_TRUE(result);
    
    // 清理资源
    ble_prov_stop();
}

// 测试用例2: 设备名称生成测试
TEST_CASE("Device name generation test", "[ble_prov]")
{
    uint8_t test_mac[6] = {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC};
    char device_name[32];
    sprintf(device_name, "PROV_%02X%02X%02X", test_mac[3], test_mac[4], test_mac[5]);
    
    TEST_ASSERT_EQUAL_STRING("PROV_789ABC", device_name);
}

// 测试用例3: 参数验证测试
TEST_CASE("Parameter validation test", "[ble_prov]")
{
    // 测试空设备名
    bool result = ble_prov_start(NULL, "123456", false);
    TEST_ASSERT_FALSE(result);
    
    // 测试空POP
    result = ble_prov_start("PROV_TEST01", NULL, false);
    TEST_ASSERT_FALSE(result);
    
    // 测试过长设备名
    char long_name[100];
    memset(long_name, 'A', 99);
    long_name[99] = '\0';
    result = ble_prov_start(long_name, "123456", false);
    TEST_ASSERT_FALSE(result);
}
```

#### 4.1.2 事件处理测试
```c
// 测试用例4: 事件回调测试
TEST_CASE("Event handler test", "[ble_prov]")
{
    // 模拟WiFi凭证接收事件
    wifi_sta_config_t test_config;
    strcpy((char*)test_config.ssid, "TestSSID");
    strcpy((char*)test_config.password, "TestPassword");
    
    // 触发事件处理
    prov_event_handler(NULL, WIFI_PROV_EVENT, WIFI_PROV_CRED_RECV, &test_config);
    
    // 验证凭证是否正确保存
    // 这里需要访问内部状态，可能需要添加测试接口
}

// 测试用例5: 超时处理测试
TEST_CASE("Timeout handling test", "[ble_prov]")
{
    // 启动配网但不提供凭证，测试超时机制
    bool result = ble_prov_start("PROV_TIMEOUT", "123456", true);
    
    // 应该在超时后返回false
    TEST_ASSERT_FALSE(result);
}
```

### 4.2 二维码模块测试 (test_qrcode.c)

#### 4.2.1 二维码生成测试
```c
// 测试用例6: 标准二维码数据格式测试
TEST_CASE("QR code data format test", "[qrcode]")
{
    const char* device_name = "PROV_123456";
    const char* pop = "123456";
    
    char expected_qr[200];
    snprintf(expected_qr, sizeof(expected_qr), 
             "{\"ver\":\"v1\",\"name\":\"%s\",\"pop\":\"%s\",\"transport\":\"ble\"}", 
             device_name, pop);
    
    // 验证生成的二维码数据格式
    // 这里需要修改print_qr_code_info函数返回生成的字符串
    char actual_qr[200];
    generate_qr_payload(device_name, pop, actual_qr, sizeof(actual_qr));
    
    TEST_ASSERT_EQUAL_STRING(expected_qr, actual_qr);
}

// 测试用例7: 二维码版本自适应测试
TEST_CASE("QR code version adaptation test", "[qrcode]")
{
    // 测试短数据（应选择版本3）
    const char* short_data = "{\"ver\":\"v1\",\"name\":\"PROV_123\",\"pop\":\"123\",\"transport\":\"ble\"}";
    uint8_t version = get_optimal_qr_version(short_data);
    TEST_ASSERT_EQUAL(3, version);
    
    // 测试长数据（应选择更高版本）
    const char* long_data = "{\"ver\":\"v1\",\"name\":\"PROV_VERY_LONG_DEVICE_NAME_FOR_TESTING\",\"pop\":\"VERY_LONG_POP_KEY_FOR_TESTING\",\"transport\":\"ble\"}";
    version = get_optimal_qr_version(long_data);
    TEST_ASSERT_GREATER_THAN(3, version);
}

// 测试用例8: 二维码生成错误处理测试
TEST_CASE("QR code generation error handling test", "[qrcode]")
{
    // 测试空数据
    print_qrcode_to_terminal(NULL);
    // 应该不崩溃，可能输出错误日志
    
    // 测试过长数据
    char very_long_data[1000];
    memset(very_long_data, 'A', 999);
    very_long_data[999] = '\0';
    print_qrcode_to_terminal(very_long_data);
    // 应该优雅处理错误
}
```

### 4.3 WiFi凭证管理测试 (test_ssid_manager.c)

#### 4.3.1 基础CRUD操作测试
```c
// 测试用例9: WiFi凭证添加测试
TEST_CASE("SSID manager add test", "[ssid_mgr]")
{
    SsidManager& manager = SsidManager::GetInstance();
    manager.Clear();
    
    // 添加第一个WiFi
    manager.AddSsid("TestSSID1", "TestPassword1");
    auto list = manager.GetSsidList();
    TEST_ASSERT_EQUAL(1, list.size());
    TEST_ASSERT_EQUAL_STRING("TestSSID1", list[0].ssid.c_str());
    TEST_ASSERT_EQUAL_STRING("TestPassword1", list[0].password.c_str());
    
    // 添加第二个WiFi
    manager.AddSsid("TestSSID2", "TestPassword2");
    list = manager.GetSsidList();
    TEST_ASSERT_EQUAL(2, list.size());
    // 新添加的应该在前面
    TEST_ASSERT_EQUAL_STRING("TestSSID2", list[0].ssid.c_str());
}

// 测试用例10: WiFi凭证覆盖测试
TEST_CASE("SSID manager overwrite test", "[ssid_mgr]")
{
    SsidManager& manager = SsidManager::GetInstance();
    manager.Clear();
    
    // 添加WiFi
    manager.AddSsid("TestSSID", "OldPassword");
    
    // 添加相同SSID但不同密码
    manager.AddSsid("TestSSID", "NewPassword");
    
    auto list = manager.GetSsidList();
    TEST_ASSERT_EQUAL(1, list.size());
    TEST_ASSERT_EQUAL_STRING("NewPassword", list[0].password.c_str());
}

// 测试用例11: WiFi列表容量限制测试
TEST_CASE("SSID manager capacity limit test", "[ssid_mgr]")
{
    SsidManager& manager = SsidManager::GetInstance();
    manager.Clear();
    
    // 添加超过最大容量的WiFi
    for (int i = 0; i < 15; i++) {
        char ssid[32], password[32];
        sprintf(ssid, "TestSSID%d", i);
        sprintf(password, "TestPassword%d", i);
        manager.AddSsid(ssid, password);
    }
    
    auto list = manager.GetSsidList();
    TEST_ASSERT_EQUAL(10, list.size()); // 最大容量为10
    
    // 验证最新的在前面，最老的被移除
    TEST_ASSERT_EQUAL_STRING("TestSSID14", list[0].ssid.c_str());
}
```

#### 4.3.2 NVS存储测试
```c
// 测试用例12: NVS持久化测试
TEST_CASE("SSID manager NVS persistence test", "[ssid_mgr]")
{
    // 第一阶段：添加数据
    {
        SsidManager& manager = SsidManager::GetInstance();
        manager.Clear();
        manager.AddSsid("PersistentSSID", "PersistentPassword");
    }
    
    // 第二阶段：重新创建实例，验证数据持久化
    {
        SsidManager& manager = SsidManager::GetInstance();
        auto list = manager.GetSsidList();
        TEST_ASSERT_EQUAL(1, list.size());
        TEST_ASSERT_EQUAL_STRING("PersistentSSID", list[0].ssid.c_str());
        TEST_ASSERT_EQUAL_STRING("PersistentPassword", list[0].password.c_str());
    }
}

// 测试用例13: NVS错误处理测试
TEST_CASE("SSID manager NVS error handling test", "[ssid_mgr]")
{
    // 模拟NVS写入失败的情况
    // 这可能需要mock NVS函数或使用依赖注入
    
    SsidManager& manager = SsidManager::GetInstance();
    
    // 在NVS不可用的情况下，应该优雅处理
    // 具体实现取决于错误处理策略
}
```

## 5. 集成测试用例

### 5.1 完整配网流程测试
```c
// 测试用例14: 端到端配网流程测试
TEST_CASE("End-to-end provisioning flow test", "[integration]")
{
    // 1. 清理环境
    SsidManager& manager = SsidManager::GetInstance();
    manager.Clear();
    
    // 2. 启动配网服务
    bool result = ble_prov_start("PROV_E2E", "123456", false);
    TEST_ASSERT_TRUE(result);
    
    // 3. 模拟接收WiFi凭证
    wifi_sta_config_t test_config;
    strcpy((char*)test_config.ssid, "TestWiFi");
    strcpy((char*)test_config.password, "TestPass");
    
    // 4. 触发凭证接收事件
    prov_event_handler(NULL, WIFI_PROV_EVENT, WIFI_PROV_CRED_RECV, &test_config);
    
    // 5. 模拟WiFi连接成功
    prov_event_handler(NULL, WIFI_PROV_EVENT, WIFI_PROV_CRED_SUCCESS, NULL);
    
    // 6. 验证凭证已保存
    auto list = manager.GetSsidList();
    TEST_ASSERT_EQUAL(1, list.size());
    TEST_ASSERT_EQUAL_STRING("TestWiFi", list[0].ssid.c_str());
    
    // 7. 清理
    ble_prov_stop();
}
```

## 6. 性能测试用例

### 6.1 内存使用测试
```c
// 测试用例15: 内存泄漏测试
TEST_CASE("Memory leak test", "[performance]")
{
    size_t initial_free_heap = esp_get_free_heap_size();
    
    // 多次启动和停止配网服务
    for (int i = 0; i < 10; i++) {
        bool result = ble_prov_start("PROV_MEM", "123456", false);
        TEST_ASSERT_TRUE(result);
        vTaskDelay(pdMS_TO_TICKS(100));
        ble_prov_stop();
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    size_t final_free_heap = esp_get_free_heap_size();
    
    // 允许一定的内存差异（考虑到系统开销）
    TEST_ASSERT_INT_WITHIN(1024, initial_free_heap, final_free_heap);
}

// 测试用例16: 二维码生成性能测试
TEST_CASE("QR code generation performance test", "[performance]")
{
    const char* test_data = "{\"ver\":\"v1\",\"name\":\"PROV_PERF\",\"pop\":\"123456\",\"transport\":\"ble\"}";
    
    uint32_t start_time = esp_timer_get_time();
    
    // 生成100次二维码
    for (int i = 0; i < 100; i++) {
        print_qrcode_to_terminal(test_data);
    }
    
    uint32_t end_time = esp_timer_get_time();
    uint32_t duration_ms = (end_time - start_time) / 1000;
    
    // 每次生成应该在合理时间内完成（例如<100ms）
    TEST_ASSERT_LESS_THAN(10000, duration_ms); // 100次总共<10秒
}
```

## 7. 测试环境配置

### 7.1 CMakeLists.txt 配置
```cmake
# 测试组件配置
idf_component_register(
    SRCS "test_ble_provisioning.c" "test_qrcode.c" "test_ssid_manager.c"
    INCLUDE_DIRS "."
    REQUIRES unity ble_provisioning qrcode ssid_manager
)
```

### 7.2 测试运行配置
```bash
# 编译测试
idf.py build

# 运行所有测试
idf.py flash monitor -p /dev/ttyUSB0

# 运行特定测试
idf.py flash monitor -p /dev/ttyUSB0 -D TEST_COMPONENTS="ble_prov"
```

## 8. Mock和Stub策略

### 8.1 WiFi Mock
```c
// 模拟WiFi初始化
esp_err_t mock_esp_wifi_init(const wifi_init_config_t *config) {
    return ESP_OK;
}

// 模拟网络接口创建
esp_netif_t* mock_esp_netif_create_default_wifi_sta(void) {
    static esp_netif_t dummy_netif;
    return &dummy_netif;
}
```

### 8.2 NVS Mock
```c
// 模拟NVS操作
esp_err_t mock_nvs_open(const char* namespace_name, nvs_open_mode_t open_mode, nvs_handle_t *out_handle) {
    *out_handle = 1; // 假句柄
    return ESP_OK;
}

esp_err_t mock_nvs_set_str(nvs_handle_t handle, const char* key, const char* value) {
    // 存储到内存中的模拟存储
    return ESP_OK;
}
```

## 9. 测试数据和边界条件

### 9.1 测试数据集
```c
// 有效的测试数据
static const test_data_t valid_test_cases[] = {
    {"PROV_123456", "123456", true},
    {"PROV_ABCDEF", "password", true},
    {"PROV_000000", "12345678", true},
};

// 无效的测试数据
static const test_data_t invalid_test_cases[] = {
    {NULL, "123456", false},           // 空设备名
    {"PROV_123456", NULL, false},      // 空POP
    {"", "123456", false},             // 空字符串设备名
    {"PROV_123456", "", false},        // 空字符串POP
};
```

### 9.2 边界条件测试
```c
// 测试用例17: 边界条件测试
TEST_CASE("Boundary conditions test", "[boundary]")
{
    // 最大长度设备名
    char max_device_name[32];
    memset(max_device_name, 'A', 31);
    max_device_name[31] = '\0';
    
    bool result = ble_prov_start(max_device_name, "123456", false);
    TEST_ASSERT_TRUE(result);
    ble_prov_stop();
    
    // 最大长度POP
    char max_pop[64];
    memset(max_pop, '1', 63);
    max_pop[63] = '\0';
    
    result = ble_prov_start("PROV_TEST", max_pop, false);
    TEST_ASSERT_TRUE(result);
    ble_prov_stop();
}
```

## 10. 持续集成配置

### 10.1 GitHub Actions 配置示例
```yaml
name: Unit Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup ESP-IDF
      uses: espressif/esp-idf-ci-action@v1
      with:
        esp_idf_version: v5.3
    - name: Build and Test
      run: |
        cd test
        idf.py build
        # 在模拟器中运行测试或使用硬件在环测试
```

## 11. 关键实现细节

### 11.1 二维码数据格式规范
```json
{
  "ver": "v1",                    // 协议版本，固定为v1
  "name": "PROV_6916EC",         // BLE设备广播名称，格式：PROV_XXXXXX
  "pop": "123456",               // 安全密钥，建议6-8位数字
  "transport": "ble"             // 传输方式，固定为ble
}
```

### 11.2 设备命名算法
```c
// 基于MAC地址生成唯一设备名
void generate_device_name(char* device_name, size_t max_len) {
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    snprintf(device_name, max_len, "PROV_%02X%02X%02X",
             mac[3], mac[4], mac[5]);
}
```

### 11.3 事件状态机
```
[IDLE] -> [BLE_ADVERTISING] -> [BLE_CONNECTED] -> [CREDENTIALS_RECEIVED]
    -> [WIFI_CONNECTING] -> [WIFI_CONNECTED] -> [PROVISIONING_COMPLETE]
                         -> [WIFI_FAILED] -> [ERROR_STATE]
```

### 11.4 NVS存储结构
```
Namespace: "wifi"
Keys:
- ssid, ssid1, ssid2, ..., ssid9     (最多10个SSID)
- password, password1, ..., password9 (对应的密码)
- force_ap                           (强制AP模式标志)
```

## 12. 错误处理和恢复策略

### 12.1 常见错误码处理
```c
typedef enum {
    BLE_PROV_ERR_NONE = 0,
    BLE_PROV_ERR_WIFI_INIT_FAILED,
    BLE_PROV_ERR_NETIF_CREATE_FAILED,
    BLE_PROV_ERR_PROV_MGR_INIT_FAILED,
    BLE_PROV_ERR_EVENT_REGISTER_FAILED,
    BLE_PROV_ERR_START_FAILED,
    BLE_PROV_ERR_TIMEOUT,
    BLE_PROV_ERR_WIFI_CONNECT_FAILED,
    BLE_PROV_ERR_INVALID_PARAMS
} ble_prov_err_t;
```

### 12.2 超时和重试机制
```c
#define PROVISIONING_TIMEOUT_MS     (120 * 1000)  // 120秒超时
#define WIFI_CONNECT_TIMEOUT_MS     (30 * 1000)   // WiFi连接30秒超时
#define MAX_RETRY_COUNT             3              // 最大重试次数
#define RETRY_DELAY_MS              5000           // 重试间隔5秒
```

## 13. 安全考虑和最佳实践

### 13.1 安全配置
```c
// 使用WIFI_PROV_SECURITY_1提供加密保护
#define PROV_SECURITY_LEVEL    WIFI_PROV_SECURITY_1

// POP密钥建议
// - 长度：6-8位
// - 复杂度：数字+字母组合
// - 避免：123456, password等弱密钥
```

### 13.2 数据保护
```c
// WiFi密码在内存中的保护
void secure_memset(void* ptr, int value, size_t num) {
    volatile unsigned char* p = ptr;
    while (num--) *p++ = value;
}

// 清除敏感数据
void clear_wifi_credentials(wifi_sta_config_t* config) {
    secure_memset(config->password, 0, sizeof(config->password));
}
```

## 14. 性能优化建议

### 14.1 内存优化
```c
// 配网完成后及时释放BLE资源
void cleanup_ble_resources(void) {
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    esp_bt_mem_release(ESP_BT_MODE_BLE);
}
```

### 14.2 功耗优化
```c
// 配网超时后进入低功耗模式
void enter_low_power_mode(void) {
    esp_wifi_set_ps(WIFI_PS_MAX_MODEM);
    esp_sleep_enable_ext0_wakeup(BOOT_GPIO, 0);
}
```

## 15. 调试和诊断工具

### 15.1 调试日志配置
```c
// 在menuconfig中启用详细日志
CONFIG_LOG_DEFAULT_LEVEL_DEBUG=y
CONFIG_LOG_MAXIMUM_LEVEL=4

// 关键日志点
ESP_LOGI(TAG, "BLE配网启动: 设备名=%s, POP=%s", device_name, pop);
ESP_LOGI(TAG, "收到WiFi凭证: SSID=%s", (char*)wifi_cfg->ssid);
ESP_LOGI(TAG, "WiFi连接状态: %s", success ? "成功" : "失败");
```

### 15.2 状态监控接口
```c
// 获取配网状态
typedef struct {
    bool is_provisioning;
    bool is_ble_connected;
    bool has_credentials;
    char current_ssid[33];
    uint32_t start_time;
    uint32_t timeout_remaining;
} prov_status_t;

prov_status_t get_provisioning_status(void);
```

## 16. 兼容性和移植指南

### 16.1 ESP-IDF版本兼容性
```
最低版本: ESP-IDF v4.4
推荐版本: ESP-IDF v5.0+
测试版本: ESP-IDF v5.3
```

### 16.2 硬件平台支持
```
ESP32: 完全支持
ESP32-S2: 不支持（无蓝牙）
ESP32-S3: 完全支持
ESP32-C3: 完全支持
ESP32-C6: 完全支持
ESP32-H2: 完全支持
```

### 16.3 移植检查清单
- [ ] 确认目标芯片支持BLE
- [ ] 检查内存配置（最少512KB RAM）
- [ ] 验证分区表配置（NVS分区）
- [ ] 确认GPIO配置（BOOT按键等）
- [ ] 测试二维码显示功能

## 17. 示例项目结构

```
esp32_ble_qr_provisioning/
├── CMakeLists.txt
├── sdkconfig.defaults
├── partitions.csv
├── main/
│   ├── CMakeLists.txt
│   ├── main.c
│   ├── ble_provisioning.c
│   ├── ble_provisioning.h
│   ├── qrcode.c
│   ├── qrcode.h
│   ├── ssid_manager.cpp
│   └── ssid_manager.h
├── components/
│   └── (第三方组件)
└── test/
    ├── CMakeLists.txt
    ├── test_main.c
    ├── test_ble_provisioning.c
    ├── test_qrcode.c
    └── test_ssid_manager.c
```

## 18. 快速开始指南

### 18.1 环境准备
```bash
# 安装ESP-IDF
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh
source export.sh

# 创建项目
idf.py create-project esp32_ble_provisioning
cd esp32_ble_provisioning
```

### 18.2 依赖配置
```yaml
# idf_component.yml
dependencies:
  espressif/qrcode: "^0.1.0"
  idf:
    version: ">=5.0"
```

### 18.3 编译和烧录
```bash
# 配置目标芯片
idf.py set-target esp32s3

# 配置项目
idf.py menuconfig

# 编译
idf.py build

# 烧录和监控
idf.py flash monitor
```

这份完整的文档提供了实现独立二维码蓝牙配网模块所需的所有技术细节、测试用例和最佳实践。您可以将此文档提供给新的Augment窗口，用于指导项目的开发工作。
