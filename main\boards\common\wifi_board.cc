#include "wifi_board.h"

#include "display.h"
#include "application.h"
#include "system_info.h"
#include "font_awesome_symbols.h"
#include "settings.h"
#include "assets/lang_config.h"

#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_http.h>
#include <esp_mqtt.h>
#include <esp_udp.h>
#include <tcp_transport.h>
#include <tls_transport.h>
#include <web_socket.h>
#include <esp_log.h>

#include <wifi_station.h>
#include <wifi_configuration_ap.h>
#include <ssid_manager.h>

// 添加蓝牙配网相关头文件
#include <nvs_flash.h>
#include <esp_efuse.h>
#include <esp_netif.h>
#include <ble_provisioning.h>
#include <esp_mac.h>


static const char *TAG = "WifiBoard";

WifiBoard::WifiBoard() {
    Settings settings("wifi", true);
    wifi_config_mode_ = settings.GetInt("force_ap") == 1;
    if (wifi_config_mode_) {
        ESP_LOGI(TAG, "force_ap is set to 1, reset to 0");
        settings.SetInt("force_ap", 0);
    }
}

std::string WifiBoard::GetBoardType() {
    return "wifi";
}


// 添加蓝牙配网函数
bool WifiBoard::TryBleProvisioning() {
    // 检查是否强制进入AP模式
    // if (wifi_config_mode_) {
    //     ESP_LOGI(TAG, "强制进入AP模式，跳过蓝牙配网");
    //     return false;
    // }
    
    // // 检查是否已有保存的WiFi网络
    // auto& ssid_manager = SsidManager::GetInstance();
    // auto ssid_list = ssid_manager.GetSsidList();
    // ESP_LOGI(TAG, "已保存的WiFi网络数量: %d", ssid_list.size());
    
    // // 如果已有保存的WiFi网络，跳过蓝牙配网
    // if (!ssid_list.empty()) {
    //     ESP_LOGI(TAG, "已有保存的WiFi网络，跳过蓝牙配网");
    //     return false;
    // }

// 删除SsidManager中的所有Wi-Fi配置
    auto& ssid_manager = SsidManager::GetInstance();
    ESP_LOGI(TAG, "清除所有已保存的WiFi网络");
    ssid_manager.Clear();
    
    // 确保ESP-NETIF已初始化
    esp_err_t ret = esp_netif_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "网络接口初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 获取MAC地址并创建唯一设备名称
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    char device_name[32];
    sprintf(device_name, "PROV_%02X%02X%02X", mac[3], mac[4], mac[5]);
    const char* pop = "123456"; // 安全密钥
    
    ESP_LOGI(TAG, "开始蓝牙配网过程...");
    ESP_LOGI(TAG, "设备名称: %s", device_name);

    // 显示蓝牙配网提示
    auto display = Board::GetInstance().GetDisplay();
    auto& application = Application::GetInstance();
    std::string hint = Lang::Strings::BLE_QR_CONFIG_HINT;
    application.Alert(Lang::Strings::WIFI_CONFIG_MODE, hint.c_str(), "", Lang::Sounds::P3_WIFICONFIG);
    
    // 启动蓝牙配网并等待完成
    bool result = ble_prov_start(device_name, pop, true);
    
    if (result) {
        ESP_LOGI(TAG, "蓝牙配网成功，将重启设备以连接WiFi");
        display->ShowNotification(Lang::Strings::CONNECTED_TO);
        // 延迟一段时间后重启
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        esp_restart();
        return true;
    } else {
        ESP_LOGW(TAG, "蓝牙配网失败或超时，将继续启动应用程序");
        return false;
    }
}



void WifiBoard::EnterWifiConfigMode() {
    auto& application = Application::GetInstance();
    application.SetDeviceState(kDeviceStateWifiConfiguring);

    auto& wifi_ap = WifiConfigurationAp::GetInstance();
    wifi_ap.SetLanguage(Lang::CODE);
    wifi_ap.SetSsidPrefix("Xiaozhi");
    wifi_ap.Start();

    // 显示 WiFi 配置 AP 的 SSID 和 Web 服务器 URL
    std::string hint = Lang::Strings::CONNECT_TO_HOTSPOT;
    hint += wifi_ap.GetSsid();
    hint += Lang::Strings::ACCESS_VIA_BROWSER;
    hint += wifi_ap.GetWebServerUrl();
    hint += "\n\n";
    
    // 播报配置 WiFi 的提示
    application.Alert(Lang::Strings::WIFI_CONFIG_MODE, hint.c_str(), "", Lang::Sounds::P3_WIFICONFIG);
    
    // Wait forever until reset after configuration
    while (true) {
        int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}

void WifiBoard::StartNetwork() {
    // User can press BOOT button while starting to enter WiFi configuration mode
    //使用boot按钮来判断是否进入WiFi配置模式
    if (wifi_config_mode_) {
        // Try BLE provisioning
        // 删除SsidManager中的所有Wi-Fi配置
        auto& ssid_manager = SsidManager::GetInstance();
        ESP_LOGI(TAG, "清除所有已保存的WiFi网络");
        ssid_manager.Clear();

        // 重启设备
        esp_restart();
        
        return;
    }

    // If no WiFi SSID is configured, enter WiFi configuration mode
    //如果蓝牙配网成功，不进入WiFi配置模式
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    if (ssid_list.empty()) {
        wifi_config_mode_ = true;
        if (TryBleProvisioning()) {
            return;
        }
        return;
    }

    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.OnScanBegin([this]() {
        auto display = Board::GetInstance().GetDisplay();
        display->ShowNotification(Lang::Strings::SCANNING_WIFI, 30000);
    });
    wifi_station.OnConnect([this](const std::string& ssid) {
        auto display = Board::GetInstance().GetDisplay();
        std::string notification = Lang::Strings::CONNECT_TO;
        notification += ssid;
        notification += "...";
        display->ShowNotification(notification.c_str(), 30000);
    });
    wifi_station.OnConnected([this](const std::string& ssid) {
        auto display = Board::GetInstance().GetDisplay();
        std::string notification = Lang::Strings::CONNECTED_TO;
        notification += ssid;
        display->ShowNotification(notification.c_str(), 30000);
    });
    // 添加延迟，确保网络初始化完成
    vTaskDelay(pdMS_TO_TICKS(500));
    
    wifi_station.Start();

    // Try to connect to WiFi, if failed, launch the WiFi configuration AP
    if (!wifi_station.WaitForConnected(60 * 1000)) {
        wifi_station.Stop();
        wifi_config_mode_ = true;
        EnterWifiConfigMode();
        return;
    }
}

Http* WifiBoard::CreateHttp() {
    return new EspHttp();
}

WebSocket* WifiBoard::CreateWebSocket() {
#ifdef CONFIG_CONNECTION_TYPE_WEBSOCKET
    std::string url = CONFIG_WEBSOCKET_URL;
    if (url.find("wss://") == 0) {
        return new WebSocket(new TlsTransport());
    } else {
        return new WebSocket(new TcpTransport());
    }
#endif
    return nullptr;
}

Mqtt* WifiBoard::CreateMqtt() {
    return new EspMqtt();
}

Udp* WifiBoard::CreateUdp() {
    return new EspUdp();
}

const char* WifiBoard::GetNetworkStateIcon() {
    if (wifi_config_mode_) {
        return FONT_AWESOME_WIFI;
    }
    auto& wifi_station = WifiStation::GetInstance();
    if (!wifi_station.IsConnected()) {
        return FONT_AWESOME_WIFI_OFF;
    }
    int8_t rssi = wifi_station.GetRssi();
    if (rssi >= -60) {
        return FONT_AWESOME_WIFI;
    } else if (rssi >= -70) {
        return FONT_AWESOME_WIFI_FAIR;
    } else {
        return FONT_AWESOME_WIFI_WEAK;
    }
}

std::string WifiBoard::GetBoardJson() {
    // Set the board type for OTA
    auto& wifi_station = WifiStation::GetInstance();
    std::string board_json = std::string("{\"type\":\"" BOARD_TYPE "\",");
    board_json += "\"name\":\"" BOARD_NAME "\",";
    if (!wifi_config_mode_) {
        board_json += "\"ssid\":\"" + wifi_station.GetSsid() + "\",";
        board_json += "\"rssi\":" + std::to_string(wifi_station.GetRssi()) + ",";
        board_json += "\"channel\":" + std::to_string(wifi_station.GetChannel()) + ",";
        board_json += "\"ip\":\"" + wifi_station.GetIpAddress() + "\",";
    }
    board_json += "\"mac\":\"" + SystemInfo::GetMacAddress() + "\"}";
    return board_json;
}

void WifiBoard::SetPowerSaveMode(bool enabled) {
    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.SetPowerSaveMode(enabled);
}

void WifiBoard::ResetWifiConfiguration() {
    // Set a flag and reboot the device to enter the network configuration mode
    {
        Settings settings("wifi", true);
        settings.SetInt("force_ap", 1);
    }
    GetDisplay()->ShowNotification(Lang::Strings::ENTERING_WIFI_CONFIG_MODE);
    vTaskDelay(pdMS_TO_TICKS(1000));
    // Reboot the device
    esp_restart();
}
