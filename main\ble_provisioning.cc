#include "ble_provisioning.h"

#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <esp_log.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_timer.h>
#include <esp_system.h>
#include <wifi_provisioning/manager.h>
#include <wifi_provisioning/scheme_ble.h>
#include <protocomm.h>
#include <protocomm_ble.h>
#include <nvs_flash.h>
#include "qrcode.h"

#include "ssid_manager.h"

static const char *TAG = "BLE_PROV";

// 标准ESP BLE Provisioning服务UUID
#define PROV_BLE_SERVICE_UUID_128   {0xb4, 0xdf, 0x5a, 0x1c, 0x3f, 0x6b, 0xf4, 0xbf, \
                                     0xea, 0x4a, 0x82, 0x03, 0x04, 0x90, 0x1a, 0x02}

// 事件组位标志
#define WIFI_PROV_SUCCESS_BIT  BIT0
#define WIFI_PROV_FAIL_BIT      BIT1

// 事件组句柄
#define PROVISIONING_TIMEOUT_MS (120 * 1000) // 120秒超时

static EventGroupHandle_t wifi_prov_event_group = NULL;
static bool provisioning_complete = false;
static bool provisioning_successful = false;
static wifi_sta_config_t received_wifi_cfg;

/**
 * @brief 生成标准ESP BLE Provisioning二维码
 */
void print_qr_code_info(const char *device_name, const char *pop)
{
    ESP_LOGI(TAG, "============= 标准ESP BLE配网二维码 ==============");
    ESP_LOGI(TAG, "设备名称: %s", device_name);
    ESP_LOGI(TAG, "安全密钥: %s", pop);
    ESP_LOGI(TAG, "传输方式: ble");
    ESP_LOGI(TAG, "版本: v1");
    
    // 标准ESP BLE Provisioning二维码格式
    char qr_payload[200];
    snprintf(qr_payload, sizeof(qr_payload), 
             "{\"ver\":\"v1\",\"name\":\"%s\",\"pop\":\"%s\",\"transport\":\"ble\"}", 
             device_name, pop);
    
    ESP_LOGI(TAG, "标准QR码内容: %s", qr_payload);
    
    print_qrcode_to_terminal(qr_payload);
}

/**
 * @brief ESP32事件回调函数
 */
static void prov_event_handler(void* handler_arg, esp_event_base_t event_base, 
                             int32_t event_id, void* event_data)
{
    if (event_base == WIFI_PROV_EVENT) {
        ESP_LOGI(TAG, "BLE配网事件: %ld", event_id);
        switch (event_id) {
            case WIFI_PROV_START:
                ESP_LOGI(TAG, "BLE配网服务已启动，等待小程序连接");
                break;
            case WIFI_PROV_CRED_RECV: {
                wifi_sta_config_t *wifi_sta_cfg = (wifi_sta_config_t *)event_data;
                ESP_LOGI(TAG, "收到WiFi凭证:");
                ESP_LOGI(TAG, "  SSID: %s", (const char *) wifi_sta_cfg->ssid);
                ESP_LOGI(TAG, "  密码: %s", (const char *) wifi_sta_cfg->password);
                
                memcpy(&received_wifi_cfg, wifi_sta_cfg, sizeof(wifi_sta_config_t));
                break;
            }
            case WIFI_PROV_CRED_SUCCESS:
                ESP_LOGI(TAG, "WiFi连接成功");
                provisioning_successful = true;
                if (wifi_prov_event_group) {
                    xEventGroupSetBits(wifi_prov_event_group, WIFI_PROV_SUCCESS_BIT);
                }
                break;
            case WIFI_PROV_CRED_FAIL: {
                wifi_prov_sta_fail_reason_t *reason = (wifi_prov_sta_fail_reason_t *)event_data;
                ESP_LOGE(TAG, "WiFi连接失败，原因: %s", 
                        (*reason == WIFI_PROV_STA_AUTH_ERROR) ? "认证失败" : "AP未找到");
                if (wifi_prov_event_group) {
                    xEventGroupSetBits(wifi_prov_event_group, WIFI_PROV_FAIL_BIT);
                }
                break;
            }
            case WIFI_PROV_END:
                ESP_LOGI(TAG, "BLE配网结束");
                provisioning_complete = true;
                wifi_prov_mgr_deinit();
                break;
        }
    } else if (event_base == PROTOCOMM_TRANSPORT_BLE_EVENT) {
        switch (event_id) {
            case PROTOCOMM_TRANSPORT_BLE_CONNECTED:
                ESP_LOGI(TAG, "小程序已连接到BLE设备");
                break;
            case PROTOCOMM_TRANSPORT_BLE_DISCONNECTED:
                ESP_LOGI(TAG, "小程序已断开BLE连接");
                break;
        }
    }
}

bool ble_prov_start(const char *device_name, const char *pop, bool wait_for_completion)
{
    esp_err_t ret;
    
    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ret = esp_wifi_init(&cfg);
    if (ret != ESP_OK && ret != ESP_ERR_WIFI_NOT_INIT) {
        ESP_LOGE(TAG, "WiFi初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 创建默认的网络接口
    esp_netif_t *sta_netif = esp_netif_create_default_wifi_sta();
    if (sta_netif == NULL) {
        ESP_LOGE(TAG, "创建WiFi STA接口失败");
        return false;
    }
    
    // 配置标准ESP BLE Provisioning
    wifi_prov_mgr_config_t config = {
        .scheme = wifi_prov_scheme_ble,
        .scheme_event_handler = WIFI_PROV_SCHEME_BLE_EVENT_HANDLER_FREE_BTDM
    };
    
    ret = wifi_prov_mgr_init(config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配网管理器初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 创建事件组
    if (wifi_prov_event_group == NULL) {
        wifi_prov_event_group = xEventGroupCreate();
    }
    
    // 注册事件处理函数
    ret = esp_event_handler_register(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, prov_event_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "注册WIFI_PROV_EVENT失败: %s", esp_err_to_name(ret));
        wifi_prov_mgr_deinit();
        return false;
    }
    
    ret = esp_event_handler_register(PROTOCOMM_TRANSPORT_BLE_EVENT, ESP_EVENT_ANY_ID, prov_event_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "注册BLE传输事件失败: %s", esp_err_to_name(ret));
        esp_event_handler_unregister(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, prov_event_handler);
        wifi_prov_mgr_deinit();
        return false;
    }
    
    // 打印标准二维码
    print_qr_code_info(device_name, pop);
    
    // 启动标准BLE配网服务
    ESP_LOGI(TAG, "启动标准ESP BLE Provisioning服务");
    ret = wifi_prov_mgr_start_provisioning(WIFI_PROV_SECURITY_0, 
                                         (const void*)pop, 
                                         device_name, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动BLE配网失败: %s", esp_err_to_name(ret));
        esp_event_handler_unregister(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, prov_event_handler);
        esp_event_handler_unregister(PROTOCOMM_TRANSPORT_BLE_EVENT, ESP_EVENT_ANY_ID, prov_event_handler);
        wifi_prov_mgr_deinit();
        return false;
    }
    
    ESP_LOGI(TAG, "标准ESP BLE Provisioning服务已启动，设备名: %s", device_name);
    
    if (wait_for_completion) {
        ESP_LOGI(TAG, "等待小程序配网完成...");
        EventBits_t bits = xEventGroupWaitBits(wifi_prov_event_group,
                                              WIFI_PROV_SUCCESS_BIT | WIFI_PROV_FAIL_BIT,
                                              pdTRUE, pdFALSE,
                                              pdMS_TO_TICKS(PROVISIONING_TIMEOUT_MS));
        
        if ((bits & WIFI_PROV_SUCCESS_BIT) && provisioning_successful) {
            ESP_LOGI(TAG, "配网成功，保存WiFi凭证");
            
            auto& ssid_manager = SsidManager::GetInstance();
            ssid_manager.AddSsid((const char*)received_wifi_cfg.ssid, 
                               (const char*)received_wifi_cfg.password);
            
            ESP_LOGI(TAG, "WiFi凭证已保存到NVS");
            return true;
        } else {
            ESP_LOGE(TAG, "配网失败或超时");
            return false;
        }
    }
    
    return true;
}

bool ble_prov_is_provisioned(void)
{
    bool provisioned = false;
    esp_err_t err = wifi_prov_mgr_is_provisioned(&provisioned);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "检查配网状态失败: %s", esp_err_to_name(err));
        return false;
    }
    return provisioned;
}

void ble_prov_stop(void)
{
    if (!provisioning_complete) {
        wifi_prov_mgr_stop_provisioning();
    }
    
    esp_event_handler_unregister(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, prov_event_handler);
    esp_event_handler_unregister(PROTOCOMM_TRANSPORT_BLE_EVENT, ESP_EVENT_ANY_ID, prov_event_handler);
    
    if (wifi_prov_event_group) {
        vEventGroupDelete(wifi_prov_event_group);
        wifi_prov_event_group = NULL;
    }
    
    ESP_LOGI(TAG, "BLE配网服务已停止");
}



