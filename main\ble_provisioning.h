#ifndef BLE_PROVISIONING_H
#define BLE_PROVISIONING_H

#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化并启动蓝牙配网
 * 
 * @param device_name 设备名称
 * @param pop 安全密钥 (Proof of Possession)
 * @param wait_for_completion 是否等待配网完成
 * @return true 如果配网成功或已经配置过
 * @return false 如果配网失败
 */
bool ble_prov_start(const char *device_name, const char *pop, bool wait_for_completion);


/**
 * @brief 在LCD上显示二维码
 * @param qr_payload 二维码信息
 */
void display_qr_code(const char *qr_payload);

/**
 * @brief 检查是否已经通过配网获取到WiFi凭证
 * 
 * @return true 如果已经获取到WiFi凭证
 * @return false 如果尚未获取到WiFi凭证
 */
bool ble_prov_is_provisioned(void);

/**
 * @brief 停止蓝牙配网并释放资源
 */
void ble_prov_stop(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_PROVISIONING_H */
