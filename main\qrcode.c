#include "qrcode.h"
#include <string.h>
#include "esp_log.h"

#define TAG "QRCODE"

// 将二维码以ASCII字符形式输出到终端日志
void print_qrcode_to_terminal(const char* qr_data) {
    ESP_LOGI(TAG, "开始生成标准ESP BLE Provisioning二维码");
    ESP_LOGI(TAG, "二维码数据长度: %d", strlen(qr_data));
    ESP_LOGI(TAG, "二维码内容: %s", qr_data);
    
    // 根据输入数据长度选择适当的版本
    uint8_t version = 3; // 默认版本
    size_t data_len = strlen(qr_data);
    if (data_len > 40) {
        version = 4;
    }
    if (data_len > 60) {
        version = 5;
    }
    if (data_len > 90) {
        version = 6;
    }
    
    ESP_LOGI(TAG, "选择二维码版本: %d (数据长度: %d)", version, data_len);
    
    // 计算缓冲区大小并创建QR码缓冲区
    uint8_t qrcodeData[qrcodegen_BUFFER_LEN_FOR_VERSION(version)];
    uint8_t tempBuffer[qrcodegen_BUFFER_LEN_FOR_VERSION(version)];
    
    // 生成QR码 - 使用LOW错误纠正级别提高成功率
    bool success = qrcodegen_encodeText(qr_data, tempBuffer, qrcodeData, 
                                      qrcodegen_Ecc_LOW, version, version, 
                                      qrcodegen_Mask_AUTO, true);
    
    if (!success) {
        ESP_LOGW(TAG, "版本%d生成失败，尝试版本%d", version, version + 1);
        version++;
        success = qrcodegen_encodeText(qr_data, tempBuffer, qrcodeData, 
                                     qrcodegen_Ecc_LOW, version, version, 
                                     qrcodegen_Mask_AUTO, true);
    }
    
    if (!success) {
        ESP_LOGE(TAG, "标准ESP BLE配网二维码生成失败");
        return;
    }
    
    // 获取QR码大小
    int qrcode_size = qrcodegen_getSize(qrcodeData);
    ESP_LOGI(TAG, "标准ESP BLE Provisioning二维码生成成功，大小: %d x %d", qrcode_size, qrcode_size);
    
    // 在日志中输出二维码 - 使用Unicode方块字符
    ESP_LOGI(TAG, "=== 标准ESP BLE Provisioning二维码 ===");
    ESP_LOGI(TAG, "请使用支持ESP BLE Provisioning的微信小程序扫描:");
    
    // 添加上边距
    for (int margin = 0; margin < 2; margin++) {
        printf("    ");
        for (int x = 0; x < qrcode_size + 4; x++) {
            printf("██");
        }
        printf("\n");
    }
    
    // 逐行输出二维码
    for (int y = 0; y < qrcode_size; y++) {
        printf("    ████"); // 左边距
        for (int x = 0; x < qrcode_size; x++) {
            // 获取二维码模块的值，true表示黑色，false表示白色
            bool is_black = qrcodegen_getModule(qrcodeData, x, y);
            // 使用Unicode方块字符显示
            printf("%s", is_black ? "██" : "  ");
        }
        printf("████\n"); // 右边距
    }
    
    // 添加下边距
    for (int margin = 0; margin < 2; margin++) {
        printf("    ");
        for (int x = 0; x < qrcode_size + 4; x++) {
            printf("██");
        }
        printf("\n");
    }
    
    ESP_LOGI(TAG, "=========================================");
    ESP_LOGI(TAG, "微信小程序搜索: ESP BLE Provisioning");
    ESP_LOGI(TAG, "或使用支持标准ESP配网协议的小程序");
}

// // 在LCD上显示二维码
// void display_qrcode_on_lcd(const char* qr_data, int x, int y) {
//     // 根据输入数据长度选择适当的版本
//     uint8_t version = 3; // 默认版本
//     if (strlen(qr_data) > 40) {
//         version = 4;
//     }
//     if (strlen(qr_data) > 60) {
//         version = 5;
//     }
//     if (strlen(qr_data) > 90) {
//         version = 6;
//     }
    
//     // 计算缓冲区大小并创建QR码缓冲区
//     uint8_t qrcodeData[qrcodegen_BUFFER_LEN_FOR_VERSION(version)];
//     uint8_t tempBuffer[qrcodegen_BUFFER_LEN_FOR_VERSION(version)];
    
//     // 生成QR码
//     bool success = qrcodegen_encodeText(qr_data, tempBuffer, qrcodeData, 
//                                       qrcodegen_Ecc_MEDIUM, version, version, 
//                                       qrcodegen_Mask_AUTO, true);
    
//     if (!success) {
//         ESP_LOGE(TAG, "二维码生成失败");
//         return;
//     }
    
//     // 获取QR码大小
//     int qrcode_size = qrcodegen_getSize(qrcodeData);
//     ESP_LOGI(TAG, "二维码生成成功，大小: %d x %d", qrcode_size, qrcode_size);
    
//     // 分配用于显示的缓冲区
//     int display_size = qrcode_size * QR_MODULE_SIZE;
//     int margin = QR_MARGIN * QR_MODULE_SIZE;
//     int buffer_width = display_size + 2 * margin;
//     int buffer_height = PARALLEL_LINES;
//     uint16_t* linedata = heap_caps_malloc(buffer_width * buffer_height * sizeof(uint16_t), MALLOC_CAP_DMA);
    
//     if (linedata == NULL) {
//         ESP_LOGE(TAG, "无法分配显示缓冲区");
//         return;
//     }
    
//     // 准备SPI事务
//     spi_transaction_t trans[6];
    
//     // 循环绘制二维码
//     for (int ypos = 0; ypos < display_size + 2 * margin; ypos += buffer_height) {
//         // 计算当前批次的实际高度
//         int current_height = buffer_height;
//         if (ypos + current_height > display_size + 2 * margin) {
//             current_height = display_size + 2 * margin - ypos;
//         }
        
//         // 填充行数据
//         for (int row = 0; row < current_height; row++) {
//             for (int col = 0; col < buffer_width; col++) {
//                 // 计算对应的二维码坐标
//                 int qr_y = (ypos + row - margin) / QR_MODULE_SIZE;
//                 int qr_x = (col - margin) / QR_MODULE_SIZE;
                
//                 // 检查是否在二维码范围内
//                 if (qr_x >= 0 && qr_x < qrcode_size && qr_y >= 0 && qr_y < qrcode_size) {
//                     // 获取二维码模块的值，1表示黑色，0表示白色
//                     bool is_black = qrcodegen_getModule(qrcodeData, qr_x, qr_y);
//                     // 设置颜色：黑色(0x0000)或白色(0xFFFF)
//                     linedata[row * buffer_width + col] = SPI_SWAP_DATA_TX(is_black ? 0x0000 : 0xFFFF, 16);
//                 } else {
//                     // 边距部分使用白色
//                     linedata[row * buffer_width + col] = SPI_SWAP_DATA_TX(0xFFFF, 16);
//                 }
//             }
//         }
        
//         // 初始化SPI事务
//         for (int i = 0; i < 6; i++) {
//             memset(&trans[i], 0, sizeof(spi_transaction_t));
//             if ((i & 1) == 0) {
//                 // 偶数事务是命令
//                 trans[i].length = 8;
//                 trans[i].user = (void*)0;
//             } else {
//                 // 奇数事务是数据
//                 trans[i].length = 8 * 4;
//                 trans[i].user = (void*)1;
//             }
//             trans[i].flags = SPI_TRANS_USE_TXDATA;
//         }
        
//         // 设置列地址范围
//         trans[0].tx_data[0] = 0x2A;         // 列地址设置命令
//         trans[1].tx_data[0] = (x + 0) >> 8;            // 起始列高字节
//         trans[1].tx_data[1] = (x + 0) & 0xff;            // 起始列低字节
//         trans[1].tx_data[2] = (x + buffer_width - 1) >> 8;   // 结束列高字节
//         trans[1].tx_data[3] = (x + buffer_width - 1) & 0xff; // 结束列低字节
        
//         // 设置行地址范围
//         trans[2].tx_data[0] = 0x2B;         // 页地址设置命令
//         trans[3].tx_data[0] = (y + ypos) >> 8;    // 起始页高字节
//         trans[3].tx_data[1] = (y + ypos) & 0xff;  // 起始页低字节
//         trans[3].tx_data[2] = (y + ypos + current_height - 1) >> 8; // 结束页高字节
//         trans[3].tx_data[3] = (y + ypos + current_height - 1) & 0xff; // 结束页低字节
        
//         trans[4].tx_data[0] = 0x2C;         // 内存写入命令
//         trans[5].tx_buffer = linedata;       // 行数据缓冲区
//         trans[5].length = buffer_width * current_height * 16;  // 数据长度，以位为单位
//         trans[5].flags = 0; // 取消SPI_TRANS_USE_TXDATA标志
        
//         // 队列所有事务
//         for (int i = 0; i < 6; i++) {
//             spi_device_queue_trans(dev_handle, &trans[i], portMAX_DELAY);
//         }
        
//         // 等待所有事务完成
//         spi_transaction_t* rtrans;
//         for (int i = 0; i < 6; i++) {
//             spi_device_get_trans_result(dev_handle, &rtrans, portMAX_DELAY);
//         }
//     }
    
//     // 释放内存
//     free(linedata);
// }

